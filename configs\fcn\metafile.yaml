Collections:
- Name: FCN
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
    - <PERSON> Context
    - Pascal Context 59
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  README: configs/fcn/README.md
  Frameworks:
  - PyTorch
Models:
- Name: fcn_r50-d8_4xb2-40k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 72.25
      mIoU(ms+flip): 73.36
  Config: configs/fcn/fcn_r50-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x1024_40k_cityscapes/fcn_r50-d8_512x1024_40k_cityscapes_20200604_192608-efe53f0d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x1024_40k_cityscapes/fcn_r50-d8_512x1024_40k_cityscapes_20200604_192608.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb2-40k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.45
      mIoU(ms+flip): 76.58
  Config: configs/fcn/fcn_r101-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x1024_40k_cityscapes/fcn_r101-d8_512x1024_40k_cityscapes_20200604_181852-a883d3a1.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x1024_40k_cityscapes/fcn_r101-d8_512x1024_40k_cityscapes_20200604_181852.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb2-40k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 71.47
      mIoU(ms+flip): 72.54
  Config: configs/fcn/fcn_r50-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_769x769_40k_cityscapes/fcn_r50-d8_769x769_40k_cityscapes_20200606_113104-977b5d02.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_769x769_40k_cityscapes/fcn_r50-d8_769x769_40k_cityscapes_20200606_113104.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb2-40k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 73.93
      mIoU(ms+flip): 75.14
  Config: configs/fcn/fcn_r101-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.4
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_769x769_40k_cityscapes/fcn_r101-d8_769x769_40k_cityscapes_20200606_113208-7d4ab69c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_769x769_40k_cityscapes/fcn_r101-d8_769x769_40k_cityscapes_20200606_113208.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r18-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 71.11
      mIoU(ms+flip): 72.91
  Config: configs/fcn/fcn_r18-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18-d8_512x1024_80k_cityscapes/fcn_r18-d8_512x1024_80k_cityscapes_20201225_021327-6c50f8b4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18-d8_512x1024_80k_cityscapes/fcn_r18-d8_512x1024_80k_cityscapes-20201225_021327.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 73.61
      mIoU(ms+flip): 74.24
  Config: configs/fcn/fcn_r50-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x1024_80k_cityscapes/fcn_r50-d8_512x1024_80k_cityscapes_20200606_113019-03aa804d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x1024_80k_cityscapes/fcn_r50-d8_512x1024_80k_cityscapes_20200606_113019.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.13
      mIoU(ms+flip): 75.94
  Config: configs/fcn/fcn_r101-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x1024_80k_cityscapes/fcn_r101-d8_512x1024_80k_cityscapes_20200606_113038-3fb937eb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x1024_80k_cityscapes/fcn_r101-d8_512x1024_80k_cityscapes_20200606_113038.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb2-amp-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.8
  Config: configs/fcn/fcn_r101-d8_4xb2-amp-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - FCN
    - (FP16)
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.37
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_fp16_512x1024_80k_cityscapes/fcn_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230921-fb13e883.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_fp16_512x1024_80k_cityscapes/fcn_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230921.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r18-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 70.8
      mIoU(ms+flip): 73.16
  Config: configs/fcn/fcn_r18-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18-d8_769x769_80k_cityscapes/fcn_r18-d8_769x769_80k_cityscapes_20201225_021451-9739d1b8.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18-d8_769x769_80k_cityscapes/fcn_r18-d8_769x769_80k_cityscapes-20201225_021451.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 72.64
      mIoU(ms+flip): 73.32
  Config: configs/fcn/fcn_r50-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_769x769_80k_cityscapes/fcn_r50-d8_769x769_80k_cityscapes_20200606_195749-f5caeabc.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_769x769_80k_cityscapes/fcn_r50-d8_769x769_80k_cityscapes_20200606_195749.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.52
      mIoU(ms+flip): 76.61
  Config: configs/fcn/fcn_r101-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_769x769_80k_cityscapes/fcn_r101-d8_769x769_80k_cityscapes_20200606_214354-45cbac68.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_769x769_80k_cityscapes/fcn_r101-d8_769x769_80k_cityscapes_20200606_214354.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r18b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 70.24
      mIoU(ms+flip): 72.77
  Config: configs/fcn/fcn_r18b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18b-d8_512x1024_80k_cityscapes/fcn_r18b-d8_512x1024_80k_cityscapes_20201225_230143-92c0f445.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18b-d8_512x1024_80k_cityscapes/fcn_r18b-d8_512x1024_80k_cityscapes-20201225_230143.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.65
      mIoU(ms+flip): 77.59
  Config: configs/fcn/fcn_r50b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50b-d8_512x1024_80k_cityscapes/fcn_r50b-d8_512x1024_80k_cityscapes_20201225_094221-82957416.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50b-d8_512x1024_80k_cityscapes/fcn_r50b-d8_512x1024_80k_cityscapes-20201225_094221.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.37
      mIoU(ms+flip): 78.77
  Config: configs/fcn/fcn_r101b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.1
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101b-d8_512x1024_80k_cityscapes/fcn_r101b-d8_512x1024_80k_cityscapes_20201226_160213-4543858f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101b-d8_512x1024_80k_cityscapes/fcn_r101b-d8_512x1024_80k_cityscapes-20201226_160213.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r18b-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 69.66
      mIoU(ms+flip): 72.07
  Config: configs/fcn/fcn_r18b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18b-d8_769x769_80k_cityscapes/fcn_r18b-d8_769x769_80k_cityscapes_20201226_004430-32d504e5.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r18b-d8_769x769_80k_cityscapes/fcn_r18b-d8_769x769_80k_cityscapes-20201226_004430.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50b-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 73.83
      mIoU(ms+flip): 76.6
  Config: configs/fcn/fcn_r50b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50b-d8_769x769_80k_cityscapes/fcn_r50b-d8_769x769_80k_cityscapes_20201225_094223-94552d38.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50b-d8_769x769_80k_cityscapes/fcn_r50b-d8_769x769_80k_cityscapes-20201225_094223.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101b-d8_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.02
      mIoU(ms+flip): 78.67
  Config: configs/fcn/fcn_r101b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101b-d8_769x769_80k_cityscapes/fcn_r101b-d8_769x769_80k_cityscapes_20201226_170012-82be37e2.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101b-d8_769x769_80k_cityscapes/fcn_r101b-d8_769x769_80k_cityscapes-20201226_170012.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50-d16_4xb2-40k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.06
      mIoU(ms+flip): 78.85
  Config: configs/fcn/fcn-d6_r50-d16_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 3.4
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_512x1024_40k_cityscapes/fcn_d6_r50-d16_512x1024_40k_cityscapes_20210305_130133-98d5d1bc.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_512x1024_40k_cityscapes/fcn_d6_r50-d16_512x1024_40k_cityscapes-20210305_130133.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50-d16_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.27
      mIoU(ms+flip): 78.88
  Config: configs/fcn/fcn-d6_r50-d16_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_512x1024_80k_cityscapes/fcn_d6_r50-d16_512x1024_80k_cityscapes_20210306_115604-133c292f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_512x1024_80k_cityscapes/fcn_d6_r50-d16_512x1024_80k_cityscapes-20210306_115604.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50-d16_4xb2-40k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.82
      mIoU(ms+flip): 78.22
  Config: configs/fcn/fcn-d6_r50-d16_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 3.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_769x769_40k_cityscapes/fcn_d6_r50-d16_769x769_40k_cityscapes_20210305_185744-1aab18ed.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_769x769_40k_cityscapes/fcn_d6_r50-d16_769x769_40k_cityscapes-20210305_185744.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50-d16_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.04
      mIoU(ms+flip): 78.4
  Config: configs/fcn/fcn-d6_r50-d16_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_769x769_80k_cityscapes/fcn_d6_r50-d16_769x769_80k_cityscapes_20210305_200413-109d88eb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50-d16_769x769_80k_cityscapes/fcn_d6_r50-d16_769x769_80k_cityscapes-20210305_200413.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101-d16_4xb2-40k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.36
      mIoU(ms+flip): 79.18
  Config: configs/fcn/fcn-d6_r101-d16_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 4.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_512x1024_40k_cityscapes/fcn_d6_r101-d16_512x1024_40k_cityscapes_20210305_130337-9cf2b450.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_512x1024_40k_cityscapes/fcn_d6_r101-d16_512x1024_40k_cityscapes-20210305_130337.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101-d16_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.46
      mIoU(ms+flip): 80.42
  Config: configs/fcn/fcn-d6_r101-d16_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_512x1024_80k_cityscapes/fcn_d6_r101-d16_512x1024_80k_cityscapes_20210308_102747-cb336445.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_512x1024_80k_cityscapes/fcn_d6_r101-d16_512x1024_80k_cityscapes-20210308_102747.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101-d16_4xb2-40k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.28
      mIoU(ms+flip): 78.95
  Config: configs/fcn/fcn-d6_r101-d16_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 5.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_769x769_40k_cityscapes/fcn_d6_r101-d16_769x769_40k_cityscapes_20210308_102453-60b114e9.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_769x769_40k_cityscapes/fcn_d6_r101-d16_769x769_40k_cityscapes-20210308_102453.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101-d16_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.06
      mIoU(ms+flip): 79.58
  Config: configs/fcn/fcn-d6_r101-d16_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_769x769_80k_cityscapes/fcn_d6_r101-d16_769x769_80k_cityscapes_20210306_120016-e33adc4f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101-d16_769x769_80k_cityscapes/fcn_d6_r101-d16_769x769_80k_cityscapes-20210306_120016.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50b-d16_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.99
      mIoU(ms+flip): 79.03
  Config: configs/fcn/fcn-d6_r50b-d16_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 3.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50b-d16_512x1024_80k_cityscapes/fcn_d6_r50b-d16_512x1024_80k_cityscapes_20210311_125550-6a0b62e9.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50b_d16_512x1024_80k_cityscapes/fcn_d6_r50b_d16_512x1024_80k_cityscapes-20210311_125550.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r50b-d16_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.86
      mIoU(ms+flip): 78.52
  Config: configs/fcn/fcn-d6_r50b-d16_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 3.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50b-d16_769x769_80k_cityscapes/fcn_d6_r50b-d16_769x769_80k_cityscapes_20210311_131012-d665f231.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r50b_d16_769x769_80k_cityscapes/fcn_d6_r50b_d16_769x769_80k_cityscapes-20210311_131012.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101b-d16_4xb2-80k_cityscapes-512x1024
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.72
      mIoU(ms+flip): 79.53
  Config: configs/fcn/fcn-d6_r101b-d16_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 4.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101b-d16_512x1024_80k_cityscapes/fcn_d6_r101b-d16_512x1024_80k_cityscapes_20210311_144305-3f2eb5b4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101b_d16_512x1024_80k_cityscapes/fcn_d6_r101b_d16_512x1024_80k_cityscapes-20210311_144305.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn-d6_r101b-d16_4xb2-80k_cityscapes-769x769
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.34
      mIoU(ms+flip): 78.91
  Config: configs/fcn/fcn-d6_r101b-d16_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D16
    - FCN
    - (D6)
    Training Resources: 4x TITAN Xp GPUS
    Memory (GB): 4.8
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101b-d16_769x769_80k_cityscapes/fcn_d6_r101b-d16_769x769_80k_cityscapes_20210311_154527-c4d8bfbc.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_d6_r101b_d16_769x769_80k_cityscapes/fcn_d6_r101b_d16_769x769_80k_cityscapes-20210311_154527.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb4-80k_ade20k-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 35.94
      mIoU(ms+flip): 37.94
  Config: configs/fcn/fcn_r50-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_80k_ade20k/fcn_r50-d8_512x512_80k_ade20k_20200614_144016-f8ac5082.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_80k_ade20k/fcn_r50-d8_512x512_80k_ade20k_20200614_144016.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-80k_ade20k-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 39.61
      mIoU(ms+flip): 40.83
  Config: configs/fcn/fcn_r101-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 12.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_80k_ade20k/fcn_r101-d8_512x512_80k_ade20k_20200615_014143-bc1809f7.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_80k_ade20k/fcn_r101-d8_512x512_80k_ade20k_20200615_014143.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb4-160k_ade20k-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 36.1
      mIoU(ms+flip): 38.08
  Config: configs/fcn/fcn_r50-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_160k_ade20k/fcn_r50-d8_512x512_160k_ade20k_20200615_100713-4edbc3b4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_160k_ade20k/fcn_r50-d8_512x512_160k_ade20k_20200615_100713.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-160k_ade20k-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 39.91
      mIoU(ms+flip): 41.4
  Config: configs/fcn/fcn_r101-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_160k_ade20k/fcn_r101-d8_512x512_160k_ade20k_20200615_105816-fd192bd5.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_160k_ade20k/fcn_r101-d8_512x512_160k_ade20k_20200615_105816.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb4-20k_voc12aug-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 67.08
      mIoU(ms+flip): 69.94
  Config: configs/fcn/fcn_r50-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_20k_voc12aug/fcn_r50-d8_512x512_20k_voc12aug_20200617_010715-52dc5306.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_20k_voc12aug/fcn_r50-d8_512x512_20k_voc12aug_20200617_010715.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-20k_voc12aug-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 71.16
      mIoU(ms+flip): 73.57
  Config: configs/fcn/fcn_r101-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_20k_voc12aug/fcn_r101-d8_512x512_20k_voc12aug_20200617_010842-0bb4e798.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_20k_voc12aug/fcn_r101-d8_512x512_20k_voc12aug_20200617_010842.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r50-d8_4xb4-40k_voc12aug-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 66.97
      mIoU(ms+flip): 69.04
  Config: configs/fcn/fcn_r50-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_40k_voc12aug/fcn_r50-d8_512x512_40k_voc12aug_20200613_161222-5e2dbf40.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r50-d8_512x512_40k_voc12aug/fcn_r50-d8_512x512_40k_voc12aug_20200613_161222.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-40k_voc12aug-512x512
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 69.91
      mIoU(ms+flip): 72.38
  Config: configs/fcn/fcn_r101-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_40k_voc12aug/fcn_r101-d8_512x512_40k_voc12aug_20200613_161240-4c8bcefd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_512x512_40k_voc12aug/fcn_r101-d8_512x512_40k_voc12aug_20200613_161240.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-40k_pascal-context-480x480
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 44.43
      mIoU(ms+flip): 45.63
  Config: configs/fcn/fcn_r101-d8_4xb4-40k_pascal-context-480x480.py
  Metadata:
    Training Data: Pascal Context
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_40k_pascal_context/fcn_r101-d8_480x480_40k_pascal_context_20210421_154757-b5e97937.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_40k_pascal_context/fcn_r101-d8_480x480_40k_pascal_context-20210421_154757.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-80k_pascal-context-480x480
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 44.13
      mIoU(ms+flip): 45.26
  Config: configs/fcn/fcn_r101-d8_4xb4-80k_pascal-context-480x480.py
  Metadata:
    Training Data: Pascal Context
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_80k_pascal_context/fcn_r101-d8_480x480_80k_pascal_context_20210421_163310-4711813f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_80k_pascal_context/fcn_r101-d8_480x480_80k_pascal_context-20210421_163310.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-40k_pascal-context-59-480x480
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 48.42
      mIoU(ms+flip): 50.4
  Config: configs/fcn/fcn_r101-d8_4xb4-40k_pascal-context-59-480x480.py
  Metadata:
    Training Data: Pascal Context 59
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_40k_pascal_context_59/fcn_r101-d8_480x480_40k_pascal_context_59_20210415_230724-8cf83682.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_40k_pascal_context_59/fcn_r101-d8_480x480_40k_pascal_context_59-20210415_230724.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
- Name: fcn_r101-d8_4xb4-80k_pascal-context-59-480x480
  In Collection: FCN
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 49.35
      mIoU(ms+flip): 51.38
  Config: configs/fcn/fcn_r101-d8_4xb4-80k_pascal-context-59-480x480.py
  Metadata:
    Training Data: Pascal Context 59
    Batch Size: 16
    Architecture:
    - R-101-D8
    - FCN
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_80k_pascal_context_59/fcn_r101-d8_480x480_80k_pascal_context_59_20210416_110804-9a6f2c94.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fcn/fcn_r101-d8_480x480_80k_pascal_context_59/fcn_r101-d8_480x480_80k_pascal_context_59-20210416_110804.log.json
  Paper:
    Title: Fully Convolutional Networks for Semantic Segmentation
    URL: https://arxiv.org/abs/1411.4038
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/fcn_head.py#L11
  Framework: PyTorch
