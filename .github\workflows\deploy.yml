name: deploy

on: push

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-n-publish:
    runs-on: ubuntu-22.04
    if: startsWith(github.event.ref, 'refs/tags')
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python 3.7
        uses: actions/setup-python@v4
        with:
          python-version: 3.7
      - name: Build MMSegmentation
        run: |
          pip install wheel
          python setup.py sdist bdist_wheel
      - name: Publish distribution to PyPI
        run: |
          pip install twine
          twine upload dist/* -u __token__ -p ${{ secrets.pypi_password }}
