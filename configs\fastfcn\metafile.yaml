Collections:
- Name: FastFCN
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  README: configs/fastfcn/README.md
  Frameworks:
  - PyTorch
Models:
- Name: fastfcn_r50-d32_jpu_aspp_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.12
      mIoU(ms+flip): 80.58
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_aspp_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.67
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_aspp_512x1024_80k_cityscapes_20210928_053722-5d1a2648.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_aspp_512x1024_80k_cityscapes_20210928_053722.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_aspp_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.52
      mIoU(ms+flip): 80.91
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_aspp_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.79
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_aspp_4x4_512x1024_80k_cityscapes_20210924_214357-72220849.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_aspp_4x4_512x1024_80k_cityscapes_20210924_214357.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_psp_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.26
      mIoU(ms+flip): 80.86
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_psp_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - PSPNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.67
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_psp_512x1024_80k_cityscapes_20210928_053722-57749bed.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_psp_512x1024_80k_cityscapes_20210928_053722.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_psp_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.76
      mIoU(ms+flip): 80.03
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_psp_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - PSPNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.94
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_psp_4x4_512x1024_80k_cityscapes_20210925_061841-77e87b0a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_psp_4x4_512x1024_80k_cityscapes_20210925_061841.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_enc_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.97
      mIoU(ms+flip): 79.92
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_enc_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - EncNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.15
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_enc_512x1024_80k_cityscapes_20210928_030036-78da5046.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_enc_512x1024_80k_cityscapes_20210928_030036.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_enc_4xb2-80k_cityscapes-512x1024
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.6
      mIoU(ms+flip): 80.25
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_enc_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D32
    - FastFCN
    - EncNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 15.45
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_enc_4x4_512x1024_80k_cityscapes_20210926_093217-e1eb6dbb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_4x4_512x1024_80k_cityscapes/fastfcn_r50-d32_jpu_enc_4x4_512x1024_80k_cityscapes_20210926_093217.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_aspp_4xb4-80k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.88
      mIoU(ms+flip): 42.91
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_aspp_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.46
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x512_80k_ade20k/fastfcn_r50-d32_jpu_aspp_512x512_80k_ade20k_20211013_190619-3aa40f2d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x512_80k_ade20k/fastfcn_r50-d32_jpu_aspp_512x512_80k_ade20k_20211013_190619.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_aspp_4xb4-160k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.58
      mIoU(ms+flip): 44.92
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_aspp_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x512_160k_ade20k/fastfcn_r50-d32_jpu_aspp_512x512_160k_ade20k_20211008_152246-27036aee.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_aspp_512x512_160k_ade20k/fastfcn_r50-d32_jpu_aspp_512x512_160k_ade20k_20211008_152246.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_psp_4xb4-80k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.4
      mIoU(ms+flip): 42.12
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_psp_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - PSPNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.02
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x512_80k_ade20k/fastfcn_r50-d32_jpu_psp_512x512_80k_ade20k_20210930_225137-993d07c8.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x512_80k_ade20k/fastfcn_r50-d32_jpu_psp_512x512_80k_ade20k_20210930_225137.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_psp_4xb4-160k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.63
      mIoU(ms+flip): 43.71
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_psp_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - PSPNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x512_160k_ade20k/fastfcn_r50-d32_jpu_psp_512x512_160k_ade20k_20211008_105455-e8f5a2fd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_psp_512x512_160k_ade20k/fastfcn_r50-d32_jpu_psp_512x512_160k_ade20k_20211008_105455.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_enc_4xb4-80k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 40.88
      mIoU(ms+flip): 42.36
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_enc_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - EncNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.67
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x512_80k_ade20k/fastfcn_r50-d32_jpu_enc_512x512_80k_ade20k_20210930_225214-65aef6dd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x512_80k_ade20k/fastfcn_r50-d32_jpu_enc_512x512_80k_ade20k_20210930_225214.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
- Name: fastfcn_r50-d32_jpu_enc_4xb4-160k_ade20k-512x512
  In Collection: FastFCN
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.5
      mIoU(ms+flip): 44.21
  Config: configs/fastfcn/fastfcn_r50-d32_jpu_enc_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D32
    - FastFCN
    - EncNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x512_160k_ade20k/fastfcn_r50-d32_jpu_enc_512x512_160k_ade20k_20211008_105456-d875ce3c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fastfcn/fastfcn_r50-d32_jpu_enc_512x512_160k_ade20k/fastfcn_r50-d32_jpu_enc_512x512_160k_ade20k_20211008_105456.log.json
  Paper:
    Title: 'FastFCN: Rethinking Dilated Convolution in the Backbone for Semantic Segmentation'
    URL: https://arxiv.org/abs/1903.11816
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/necks/jpu.py#L12
  Framework: PyTorch
