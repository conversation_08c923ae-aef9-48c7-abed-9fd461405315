Collections:
- Name: LRASPP
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
  Paper:
    Title: Searching for MobileNetV3
    URL: https://arxiv.org/abs/1905.02244
  README: configs/mobilenet_v3/README.md
  Frameworks:
  - PyTorch
Models:
- Name: mobilenet-v3-d8_lraspp_4xb4-320k_cityscapes-512x1024
  In Collection: LRASPP
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 69.54
      mIoU(ms+flip): 70.89
  Config: configs/mobilenet_v3/mobilenet-v3-d8_lraspp_4xb4-320k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 16
    Architecture:
    - M-V3-D8
    - LRASPP
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3-d8_512x1024_320k_cityscapes/lraspp_m-v3-d8_512x1024_320k_cityscapes_20201224_220337-cfe8fb07.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3-d8_512x1024_320k_cityscapes/lraspp_m-v3-d8_512x1024_320k_cityscapes-20201224_220337.log.json
  Paper:
    Title: Searching for MobileNetV3
    URL: https://arxiv.org/abs/1905.02244
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/backbones/mobilenet_v3.py#L15
  Framework: PyTorch
- Name: mobilenet-v3-d8-scratch_lraspp_4xb4-320k_cityscapes-512x1024
  In Collection: LRASPP
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 67.87
      mIoU(ms+flip): 69.78
  Config: configs/mobilenet_v3/mobilenet-v3-d8-scratch_lraspp_4xb4-320k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 16
    Architecture:
    - M-V3-D8
    - LRASPP
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3-d8_scratch_512x1024_320k_cityscapes/lraspp_m-v3-d8_scratch_512x1024_320k_cityscapes_20201224_220337-9f29cd72.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3-d8_scratch_512x1024_320k_cityscapes/lraspp_m-v3-d8_scratch_512x1024_320k_cityscapes-20201224_220337.log.json
  Paper:
    Title: Searching for MobileNetV3
    URL: https://arxiv.org/abs/1905.02244
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/backbones/mobilenet_v3.py#L15
  Framework: PyTorch
- Name: mobilenet-v3-d8-s_lraspp_4xb4-320k_cityscapes-512x1024
  In Collection: LRASPP
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 64.11
      mIoU(ms+flip): 66.42
  Config: configs/mobilenet_v3/mobilenet-v3-d8-s_lraspp_4xb4-320k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 16
    Architecture:
    - M-V3s-D8
    - LRASPP
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3s-d8_512x1024_320k_cityscapes/lraspp_m-v3s-d8_512x1024_320k_cityscapes_20201224_223935-61565b34.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3s-d8_512x1024_320k_cityscapes/lraspp_m-v3s-d8_512x1024_320k_cityscapes-20201224_223935.log.json
  Paper:
    Title: Searching for MobileNetV3
    URL: https://arxiv.org/abs/1905.02244
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/backbones/mobilenet_v3.py#L15
  Framework: PyTorch
- Name: mobilenet-v3-d8-scratch-s_lraspp_4xb4-320k_cityscapes-512x1024
  In Collection: LRASPP
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 62.74
      mIoU(ms+flip): 65.01
  Config: configs/mobilenet_v3/mobilenet-v3-d8-scratch-s_lraspp_4xb4-320k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 16
    Architecture:
    - M-V3s-D8
    - LRASPP
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3s-d8_scratch_512x1024_320k_cityscapes/lraspp_m-v3s-d8_scratch_512x1024_320k_cityscapes_20201224_223935-03daeabb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/mobilenet_v3/lraspp_m-v3s-d8_scratch_512x1024_320k_cityscapes/lraspp_m-v3s-d8_scratch_512x1024_320k_cityscapes-20201224_223935.log.json
  Paper:
    Title: Searching for MobileNetV3
    URL: https://arxiv.org/abs/1905.02244
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/backbones/mobilenet_v3.py#L15
  Framework: PyTorch
