import cv2
import numpy as np
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import os
from PIL import Image

def generate_distinct_colors(n_colors):
    """生成n个区分度较高的颜色"""
    if n_colors <= 8:
        # 预定义的高区分度颜色
        distinct_colors = [
            [255, 0, 0],      # 红色
            [0, 255, 0],      # 绿色
            [0, 0, 255],      # 蓝色
            [255, 255, 0],    # 黄色
            [255, 0, 255],    # 洋红
            [0, 255, 255],    # 青色
            [255, 128, 0],    # 橙色
            [128, 0, 255],    # 紫色
        ]
        return np.array(distinct_colors[:n_colors])
    else:
        # 使用HSV色彩空间生成均匀分布的颜色
        colors = []
        for i in range(n_colors):
            hue = int(180 * i / n_colors)  # 色调均匀分布
            hsv_color = np.array([[[hue, 255, 255]]], dtype=np.uint8)
            rgb_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2RGB)[0, 0]
            colors.append(rgb_color)
        return np.array(colors)

def color_clustering(image_path, n_clusters=8, output_path=None):
    """
    对图像进行颜色聚类，让相似颜色使用同一RGB值，不同区域颜色区分更明显
    
    参数:
    - image_path: 输入图像路径
    - n_clusters: 聚类数量（颜色种类数）
    - output_path: 输出图像路径，如果为None则自动生成
    """
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    # 转换为RGB格式
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape
    
    # 将图像重塑为像素数组 (height*width, 3)
    pixels = image_rgb.reshape(-1, 3)
    
    print(f"图像尺寸: {original_shape}")
    print(f"像素总数: {len(pixels)}")
    print(f"聚类数量: {n_clusters}")
    
    # 使用K-means进行颜色聚类
    print("正在进行颜色聚类...")
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    labels = kmeans.fit_predict(pixels)
    
    # 生成区分度高的颜色
    distinct_colors = generate_distinct_colors(n_clusters)
    
    # 创建新的图像
    new_pixels = distinct_colors[labels]
    clustered_image = new_pixels.reshape(original_shape)
    
    # 生成输出路径
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = f"my_tools/out/111/{base_name}_clustered_{n_clusters}colors.png"
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果
    clustered_image_bgr = cv2.cvtColor(clustered_image.astype(np.uint8), cv2.COLOR_RGB2BGR)
    cv2.imwrite(output_path, clustered_image_bgr)
    
    # 显示结果对比
    plt.figure(figsize=(15, 5))
    
    # 原图
    plt.subplot(1, 3, 1)
    plt.imshow(image_rgb)
    plt.title('原始图像')
    plt.axis('off')
    
    # 聚类结果
    plt.subplot(1, 3, 2)
    plt.imshow(clustered_image.astype(np.uint8))
    plt.title(f'颜色聚类结果 ({n_clusters}种颜色)')
    plt.axis('off')
    
    # 颜色调色板
    plt.subplot(1, 3, 3)
    palette = distinct_colors.reshape(1, n_clusters, 3)
    plt.imshow(palette.astype(np.uint8))
    plt.title('使用的颜色调色板')
    plt.axis('off')
    
    plt.tight_layout()
    
    # 保存对比图
    comparison_path = f"my_tools/out/222/{os.path.splitext(os.path.basename(image_path))[0]}_comparison.png"
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"处理完成!")
    print(f"聚类结果保存至: {output_path}")
    print(f"对比图保存至: {comparison_path}")
    
    # 打印颜色信息
    print("\n使用的颜色 (RGB值):")
    for i, color in enumerate(distinct_colors):
        print(f"颜色 {i+1}: RGB({color[0]}, {color[1]}, {color[2]})")
    
    return clustered_image, distinct_colors

def batch_process(input_dir, n_clusters=8):
    """批量处理文件夹中的所有图像"""
    
    if not os.path.exists(input_dir):
        print(f"输入目录不存在: {input_dir}")
        return
    
    # 支持的图像格式
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
    
    # 获取所有图像文件
    image_files = [f for f in os.listdir(input_dir) 
                   if f.lower().endswith(supported_formats)]
    
    if not image_files:
        print(f"在目录 {input_dir} 中没有找到支持的图像文件")
        return
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    for i, filename in enumerate(image_files, 1):
        print(f"\n处理第 {i}/{len(image_files)} 个文件: {filename}")
        image_path = os.path.join(input_dir, filename)
        
        try:
            color_clustering(image_path, n_clusters)
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            continue

if __name__ == "__main__":
    # 使用示例
    
    # 方式1: 处理单张图像
    # image_path = "path/to/your/image.jpg"  # 替换为您的图像路径
    # color_clustering(image_path, n_clusters=6)
    
    # 方式2: 批量处理
    input_directory = "C:/qyh/samples/save_img"  # 替换为您的图像文件夹路径
    
    print("颜色聚类工具")
    print("=" * 50)
    
    # 检查输入目录是否存在
    if os.path.exists(input_directory):
        print(f"输入目录: {input_directory}")
        
        # 让用户选择聚类数量
        try:
            n_colors = int(input("请输入要聚类的颜色数量 (建议4-12): ") or "8")
            if n_colors < 2:
                n_colors = 2
            elif n_colors > 20:
                n_colors = 20
        except ValueError:
            n_colors = 8
            
        print(f"使用 {n_colors} 种颜色进行聚类")
        batch_process(input_directory, n_colors)
    else:
        print(f"输入目录不存在: {input_directory}")
        print("请修改代码中的 input_directory 变量为正确的路径")
