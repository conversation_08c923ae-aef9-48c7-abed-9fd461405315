Collections:
- Name: PSANet
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/He<PERSON><PERSON><PERSON>_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  README: configs/psanet/README.md
  Frameworks:
  - PyTorch
Models:
- Name: psanet_r50-d8_4xb2-40k_cityscapes-512x1024
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.63
      mIoU(ms+flip): 79.04
  Config: configs/psanet/psanet_r50-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 7.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x1024_40k_cityscapes/psanet_r50-d8_512x1024_40k_cityscapes_20200606_103117-99fac37c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x1024_40k_cityscapes/psanet_r50-d8_512x1024_40k_cityscapes_20200606_103117.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb2-40k_cityscapes-512x1024
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.14
      mIoU(ms+flip): 80.19
  Config: configs/psanet/psanet_r101-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x1024_40k_cityscapes/psanet_r101-d8_512x1024_40k_cityscapes_20200606_001418-27b9cfa7.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x1024_40k_cityscapes/psanet_r101-d8_512x1024_40k_cityscapes_20200606_001418.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb2-40k_cityscapes-769x769
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.99
      mIoU(ms+flip): 79.64
  Config: configs/psanet/psanet_r50-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 7.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_769x769_40k_cityscapes/psanet_r50-d8_769x769_40k_cityscapes_20200530_033717-d5365506.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_769x769_40k_cityscapes/psanet_r50-d8_769x769_40k_cityscapes_20200530_033717.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb2-40k_cityscapes-769x769
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.43
      mIoU(ms+flip): 80.26
  Config: configs/psanet/psanet_r101-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 11.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_769x769_40k_cityscapes/psanet_r101-d8_769x769_40k_cityscapes_20200530_035107-997da1e6.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_769x769_40k_cityscapes/psanet_r101-d8_769x769_40k_cityscapes_20200530_035107.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb2-80k_cityscapes-512x1024
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.24
      mIoU(ms+flip): 78.69
  Config: configs/psanet/psanet_r50-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x1024_80k_cityscapes/psanet_r50-d8_512x1024_80k_cityscapes_20200606_161842-ab60a24f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x1024_80k_cityscapes/psanet_r50-d8_512x1024_80k_cityscapes_20200606_161842.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb2-80k_cityscapes-512x1024
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.31
      mIoU(ms+flip): 80.53
  Config: configs/psanet/psanet_r101-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x1024_80k_cityscapes/psanet_r101-d8_512x1024_80k_cityscapes_20200606_161823-0f73a169.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x1024_80k_cityscapes/psanet_r101-d8_512x1024_80k_cityscapes_20200606_161823.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb2-80k_cityscapes-769x769
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.31
      mIoU(ms+flip): 80.91
  Config: configs/psanet/psanet_r50-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_769x769_80k_cityscapes/psanet_r50-d8_769x769_80k_cityscapes_20200606_225134-fe42f49e.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_769x769_80k_cityscapes/psanet_r50-d8_769x769_80k_cityscapes_20200606_225134.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb2-80k_cityscapes-769x769
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.69
      mIoU(ms+flip): 80.89
  Config: configs/psanet/psanet_r101-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_769x769_80k_cityscapes/psanet_r101-d8_769x769_80k_cityscapes_20200606_214550-7665827b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_769x769_80k_cityscapes/psanet_r101-d8_769x769_80k_cityscapes_20200606_214550.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb4-80k_ade20k-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.14
      mIoU(ms+flip): 41.91
  Config: configs/psanet/psanet_r50-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_80k_ade20k/psanet_r50-d8_512x512_80k_ade20k_20200614_144141-835e4b97.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_80k_ade20k/psanet_r50-d8_512x512_80k_ade20k_20200614_144141.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb4-80k_ade20k-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.8
      mIoU(ms+flip): 44.75
  Config: configs/psanet/psanet_r101-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 12.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_80k_ade20k/psanet_r101-d8_512x512_80k_ade20k_20200614_185117-1fab60d4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_80k_ade20k/psanet_r101-d8_512x512_80k_ade20k_20200614_185117.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb4-160k_ade20k-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.67
      mIoU(ms+flip): 42.95
  Config: configs/psanet/psanet_r50-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_160k_ade20k/psanet_r50-d8_512x512_160k_ade20k_20200615_161258-148077dd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_160k_ade20k/psanet_r50-d8_512x512_160k_ade20k_20200615_161258.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb4-160k_ade20k-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.74
      mIoU(ms+flip): 45.38
  Config: configs/psanet/psanet_r101-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_160k_ade20k/psanet_r101-d8_512x512_160k_ade20k_20200615_161537-dbfa564c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_160k_ade20k/psanet_r101-d8_512x512_160k_ade20k_20200615_161537.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb4-20k_voc12aug-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.39
      mIoU(ms+flip): 77.34
  Config: configs/psanet/psanet_r50-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_20k_voc12aug/psanet_r50-d8_512x512_20k_voc12aug_20200617_102413-2f1bbaa1.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_20k_voc12aug/psanet_r50-d8_512x512_20k_voc12aug_20200617_102413.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb4-20k_voc12aug-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.91
      mIoU(ms+flip): 79.3
  Config: configs/psanet/psanet_r101-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.4
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_20k_voc12aug/psanet_r101-d8_512x512_20k_voc12aug_20200617_110624-946fef11.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_20k_voc12aug/psanet_r101-d8_512x512_20k_voc12aug_20200617_110624.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r50-d8_4xb4-40k_voc12aug-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.3
      mIoU(ms+flip): 77.35
  Config: configs/psanet/psanet_r50-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_40k_voc12aug/psanet_r50-d8_512x512_40k_voc12aug_20200613_161946-f596afb5.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r50-d8_512x512_40k_voc12aug/psanet_r50-d8_512x512_40k_voc12aug_20200613_161946.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
- Name: psanet_r101-d8_4xb4-40k_voc12aug-512x512
  In Collection: PSANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.73
      mIoU(ms+flip): 79.05
  Config: configs/psanet/psanet_r101-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - PSANet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_40k_voc12aug/psanet_r101-d8_512x512_40k_voc12aug_20200613_161946-1f560f9e.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/psanet/psanet_r101-d8_512x512_40k_voc12aug/psanet_r101-d8_512x512_40k_voc12aug_20200613_161946.log.json
  Paper:
    Title: 'PSANet: Point-wise Spatial Attention Network for Scene Parsing'
    URL: https://openaccess.thecvf.com/content_ECCV_2018/papers/Hengshuang_Zhao_PSANet_Point-wise_Spatial_ECCV_2018_paper.pdf
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psa_head.py#L18
  Framework: PyTorch
